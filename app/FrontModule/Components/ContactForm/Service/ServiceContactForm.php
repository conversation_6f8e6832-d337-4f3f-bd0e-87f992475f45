<?php declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm\Service;

use App\FrontModule\Components\ContactForm\ContactForm;
use Nette\Application\UI\Form;

class ServiceContactForm extends ContactForm
{
	protected function createComponentForm(): Form
	{
		$form = parent::createComponentForm();

		$form->addGroup("form_label_billing_address");
		$form->addText('billingStreet', 'form_label_street')->setRequired();
		$form->addText('billingCity', 'form_label_city')->setRequired();
		$form->addText('billingZip', 'form_label_zip')->setRequired();
		$form->addText('billingIco', 'form_label_ico');


		$form->addGroup("form_label_service_type");
		$form->addCheckboxList('serviceType', 'form_label_service_type', [
			'reupholstery' => 'form_checkbox_reupholstery',
			'refinishing' => 'form_checkbox_refinishing',
			'coloring' => 'form_checkbox_coloring',
			'repair' => 'form_checkbox_repair',
			'exchange' => 'form_checkbox_exchange',
			'other' => 'form_checkbox_other',
		]);

		return $form;
	}

}
